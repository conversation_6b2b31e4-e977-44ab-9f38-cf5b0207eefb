name: facelog
description: "Take photo day to day..."

version: 1.6.7+25

environment:
  sdk: '>=3.3.1 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # General
  path: ^1.8.3
  image: ^4.1.3

  # permissions
  permission_handler: ^12.0.0+1

  # calendar view
  syncfusion_flutter_calendar: ^30.1.40
  syncfusion_flutter_core: ^30.1.40

  # camera
  camera: ^0.10.5

  # get images from assets
  path_provider: ^2.1.1

  # Date Formatter
  intl: ^0.20.2

  # font style
  google_fonts: ^6.1.0

  # notifications
  flutter_local_notifications: ^19.1.0
  timezone: ^0.10.1
  quiver: ^3.2.1

  # video music selection
  # edit video
  ffmpeg_kit_flutter_new: ^2.0.0
  video_player: ^2.8.2
  file_picker: ^10.2.0
  audioplayers: ^6.0.0

  # rate us sheet
  shared_preferences: ^2.2.2
  flutter_rating_bar: ^4.0.1

  # accelerometer
  sensors_plus: ^6.1.1

  # edit Photo (mirror)
  image_editor: ^1.3.0

  rxdart: ^0.28.0

  # communication to customer
  share_plus: ^11.0.0
  url_launcher: ^6.2.2
  mailto: ^2.0.0

  # Hive
  hive_flutter: ^1.1.0
  hive_generator: ^2.0.1

  # disable/enable auto screen off
  wakelock_plus: ^1.1.4

  # svg support
  flutter_svg: ^2.0.9

  # State Management
  provider: ^6.1.2
  equatable: ^2.0.5

  # loading page plugins
  shimmer: ^3.0.0

  # random date for load example
  random_date: ^0.0.7

  # get device info for feedback
  device_info_plus: ^11.2.1

  # Tutorial
  introduction_screen: ^3.1.12
  showcaseview: ^4.0.0

  # social media icons
  font_awesome_flutter: ^10.7.0

  # Responsive
  flutter_screenutil: ^5.9.0
  # device_preview: ^1.2.0

  # Pinch Zoom
  pinch_zoom_release_unzoom: ^2.0.0

  # Localization
  easy_localization: ^3.0.5

  # Photo Zoom View
  photo_view: ^0.15.0

  # See More/Less Formatter
  detectable_text_field: ^3.0.2

  # Firebase Services
  firebase_core: ^3.2.0
  cloud_firestore: ^5.1.0
  firebase_messaging: ^15.0.3
  firebase_app_installations: ^0.3.0+3
  firebase_performance: ^0.10.0+4
  firebase_analytics: ^11.2.1

  # Connectivity
  connectivity_plus: ^6.0.3

  # Geolocation
  geolocator: ^14.0.0
  geocoding: ^4.0.0

  # Seo more/less
  readmore: ^3.0.0

  # Auto Size Text
  auto_size_text: ^3.0.0

  # Slider
  syncfusion_flutter_sliders: ^30.1.40

  # Animation Support
  animate_do: ^4.2.0

  # In App Purchase
  in_app_purchase: ^3.2.0
  in_app_purchase_android: ^0.4.0+1
  in_app_purchase_storekit: ^0.4.3

  # App Info
  package_info_plus: ^8.0.0

  # Store redirect
  store_redirect: ^2.0.2

  # Message to User
  fluttertoast: ^8.2.5

  # App Review
  in_app_review: ^2.0.9

  # Toggle List
  toggle_list: ^0.3.1

  # ML kit
  google_mlkit_face_detection: ^0.13.1

  # GetX
  get: ^4.6.6

  # read file metadata
  native_exif: ^0.6.0

  #thumbnails:
  # video_thumbnail: ^0.5.3
  get_thumbnail_video: ^0.7.3

  # contracts viewer
  markdown_widget: ^2.3.2+6

  # Splash Screen
  flutter_native_splash: ^2.4.1

  # Instagram page dots
  smooth_page_indicator: ^1.2.0+3

  # Network Image
  cached_network_image: ^3.4.0

  # animation
  lottie: ^3.1.2

  # 3d button
  pushable_button: ^0.0.4

  # Vibrate
  vibration: ^3.1.3
  
  flutter_native_timezone_2025: ^1.0.1


dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.8

  flutter_lints: ^6.0.0

# Splash Screen Settings
flutter_native_splash:
  color: "#000000" # Arka plan rengi
  image: assets/images/SplashScreen/facelog600.png # Splash görüntüsü
  android_12:
    image: assets/images/SplashScreen/facelog1152.png # Android 12+ için splash görüntüsü
    color: "#000000" # Arka plan rengi

flutter:
  uses-material-design: true
  assets:
  # ! Buraya eklenenleri asset_path dosyasına da ekle
  # Camera
    - assets/images/camera/

    - assets/audio/

  # Face Ref Parts
    - assets/images/faceRefParts/

  # Localization Support
    - assets/translations/
    - assets/markdown/

  #buy me a coffe
    - assets/

  # animations
    - assets/animations/

  # Streak
    - assets/images/streak/



  fonts:
    - family: FacelogIcon
      fonts:
        - asset: assets\icons\FacelogIcon.ttf